const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();

console.log("🔧 Starting SIMPLE test server...");

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Simple test route
app.get("/", (req, res) => {
  console.log("📞 Health check called");
  res.json({ message: "✅ Simple server working!" });
});

// Simple POST test route
app.post("/test", (req, res) => {
  console.log("📤 POST test called");
  console.log("📝 Received data:", req.body);
  res.json({ 
    message: "✅ POST test successful!",
    received: req.body 
  });
});

// Connect to MongoDB
console.log("🔗 Connecting to MongoDB...");
mongoose.connect(process.env.MONGO_URI)
.then(() => {
  console.log("✅ MongoDB connected");
})
.catch(err => {
  console.error("❌ MongoDB error:", err);
});

// Start server
const PORT = 5002;
console.log(`🚀 Starting server on port ${PORT}...`);

app.listen(PORT, () => {
  console.log(`✅ Simple server running on http://localhost:${PORT}`);
  console.log(`🌐 Test health: http://localhost:${PORT}/`);
  console.log(`📤 Test POST: http://localhost:${PORT}/test`);
});
