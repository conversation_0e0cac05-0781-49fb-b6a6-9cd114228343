const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
require("dotenv").config();

// User Schema (same as your model)
const userSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, "User ID is required"],
    unique: true,
    trim: true,
    minlength: [3, "User ID must be at least 3 characters long"],
    maxlength: [20, "User ID must be less than 20 characters"]
  },
  name: {
    type: String,
    required: [true, "Name is required"],
    trim: true,
    minlength: [2, "Name must be at least 2 characters long"]
  },
  email: {
    type: String,
    required: [true, "Email is required"],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, "Please enter a valid email"]
  },
  password: {
    type: String,
    required: [true, "Password is required"],
    minlength: [6, "Password must be at least 6 characters long"]
  },
  dateOfBirth: {
    type: Date,
    required: [true, "Date of birth is required"]
  }
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre("save", async function(next) {
  if (!this.isModified("password")) return next();
  
  try {
    const hashedPassword = await bcrypt.hash(this.password, 12);
    this.password = hashedPassword;
    next();
  } catch (error) {
    next(error);
  }
});

const User = mongoose.model("User", userSchema);

const testDirectDB = async () => {
  console.log('🔗 Testing DIRECT MongoDB insertion...\n');

  try {
    // Connect to MongoDB
    console.log('🔗 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB Atlas!');

    // Test data
    const userData = {
      userId: "directtest001",
      name: "Direct Test User",
      email: "<EMAIL>",
      password: "password123",
      dateOfBirth: new Date("1990-01-01")
    };

    console.log('\n📤 Inserting user data directly:');
    console.log('👤 User ID:', userData.userId);
    console.log('📧 Email:', userData.email);
    console.log('👨 Name:', userData.name);
    console.log('🎂 Date of Birth:', userData.dateOfBirth);

    // Create and save user
    const newUser = new User(userData);
    const savedUser = await newUser.save();

    console.log('\n✅ SUCCESS! User saved to MongoDB!');
    console.log('🆔 Database ID:', savedUser._id);
    console.log('👤 User ID:', savedUser.userId);
    console.log('📧 Email:', savedUser.email);
    console.log('👨 Name:', savedUser.name);
    console.log('🔐 Password Hashed:', savedUser.password.startsWith('$2b$') ? 'YES' : 'NO');
    console.log('📅 Created At:', savedUser.createdAt);

    // Test: Try to find the user
    console.log('\n🔍 Testing user retrieval...');
    const foundUser = await User.findOne({ userId: userData.userId });
    
    if (foundUser) {
      console.log('✅ User found in database!');
      console.log('📧 Found email:', foundUser.email);
    } else {
      console.log('❌ User not found in database!');
    }

    // Test: Check password comparison
    console.log('\n🔐 Testing password verification...');
    const isPasswordValid = await bcrypt.compare("password123", savedUser.password);
    console.log('🔑 Password verification:', isPasswordValid ? 'PASSED' : 'FAILED');

  } catch (error) {
    console.error('❌ FAILED to insert data!');
    console.error('🔍 Error Details:');
    console.error('📝 Message:', error.message);
    console.error('🏷️ Name:', error.name);
    
    if (error.name === 'ValidationError') {
      console.error('📋 Validation Errors:');
      Object.values(error.errors).forEach((err, index) => {
        console.error(`   ${index + 1}. ${err.message}`);
      });
    }
    
    if (error.code === 11000) {
      console.error('🔄 Duplicate Key Error - User already exists');
    }
  } finally {
    // Close connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
};

testDirectDB();
