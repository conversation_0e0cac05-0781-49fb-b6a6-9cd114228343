const axios = require('axios');

const testSimplePost = async () => {
  console.log('🧪 Testing SIMPLE POST...\n');

  const testData = {
    "name": "Test User",
    "email": "<EMAIL>",
    "message": "Hello from raw POST!"
  };

  console.log('📤 Sending to simple server:');
  console.log(JSON.stringify(testData, null, 2));

  try {
    const response = await axios.post('http://localhost:5002/test', testData, {
      headers: { 'Content-Type': 'application/json' }
    });

    console.log('\n✅ SUCCESS!');
    console.log('📊 Status:', response.status);
    console.log('📝 Response:');
    console.log(JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ FAILED!');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
};

testSimplePost();
