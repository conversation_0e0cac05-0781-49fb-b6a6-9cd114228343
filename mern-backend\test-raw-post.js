const axios = require('axios');

const testRawPost = async () => {
  console.log('🚀 Testing RAW JSON POST to MongoDB...\n');

  // Raw JSON data - exactly like Postman
  const rawJsonData = {
    "userId": "testuser001",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "securepass123",
    "dateOfBirth": "1995-06-15"
  };

  console.log('📤 Sending RAW JSON data:');
  console.log(JSON.stringify(rawJsonData, null, 2));
  console.log('\n🎯 Target: http://localhost:5001/api/auth/register');
  console.log('📋 Method: POST');
  console.log('📄 Content-Type: application/json\n');

  try {
    const response = await axios({
      method: 'POST',
      url: 'http://localhost:5001/api/auth/register',
      data: rawJsonData,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 15000
    });

    console.log('✅ SUCCESS! Data posted to MongoDB!');
    console.log('📊 Response Status:', response.status);
    console.log('📝 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.user) {
      console.log('\n🎉 USER CREATED IN DATABASE:');
      console.log('👤 User ID:', response.data.user.userId);
      console.log('📧 Email:', response.data.user.email);
      console.log('👨 Name:', response.data.user.name);
      console.log('🎂 Date of Birth:', response.data.user.dateOfBirth);
      console.log('🔑 JWT Token Generated:', response.data.token ? 'YES' : 'NO');
    }

  } catch (error) {
    console.error('❌ FAILED to post data!');
    console.error('🔍 Error Details:');
    
    if (error.response) {
      // Server responded with error
      console.error('📊 Status Code:', error.response.status);
      console.error('📝 Error Message:', error.response.data?.message || 'No message');
      console.error('🔍 Full Response:');
      console.error(JSON.stringify(error.response.data, null, 2));
      
      if (error.response.data?.errors) {
        console.error('\n📋 Validation Errors:');
        error.response.data.errors.forEach((err, index) => {
          console.error(`   ${index + 1}. ${err}`);
        });
      }
      
    } else if (error.request) {
      // Request made but no response
      console.error('🌐 Network Error: No response from server');
      console.error('🔗 Request URL:', error.config?.url);
      console.error('📡 Request Method:', error.config?.method);
      
    } else {
      // Something else happened
      console.error('⚠️ Unexpected Error:', error.message);
    }
  }
};

// Test 2: Try with different data
const testSecondUser = async () => {
  console.log('\n\n🔄 Testing SECOND user registration...\n');

  const secondUser = {
    "userId": "testuser002", 
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "password": "mypassword456",
    "dateOfBirth": "1992-03-22"
  };

  console.log('📤 Sending SECOND user data:');
  console.log(JSON.stringify(secondUser, null, 2));

  try {
    const response = await axios.post('http://localhost:5001/api/auth/register', secondUser, {
      headers: { 'Content-Type': 'application/json' }
    });

    console.log('✅ SECOND USER SUCCESS!');
    console.log('👤 Created:', response.data.user?.name);
    console.log('📧 Email:', response.data.user?.email);

  } catch (error) {
    console.error('❌ Second user failed:', error.response?.data?.message || error.message);
  }
};

// Run the tests
const runTests = async () => {
  await testRawPost();
  await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
  await testSecondUser();
};

runTests();
