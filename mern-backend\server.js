const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();

console.log("🔧 Starting server setup...");

// Import routes
console.log("📁 Loading routes...");
const authRoutes = require("./routes/auth");
console.log("✅ Auth routes loaded");
const dashboardRoutes = require("./routes/dashboard");
console.log("✅ Dashboard routes loaded");
const profileRoutes = require("./routes/profile");
console.log("✅ Profile routes loaded");

const app = express();
console.log("✅ Express app created");

// Middleware
app.use(cors());
app.use(express.json());

// Connect to MongoDB Atlas
mongoose.connect(process.env.MONGO_URI)
.then(() => console.log("✅ Connected to MongoDB Atlas"))
.catch(err => console.error("❌ MongoDB connection error:", err));

// Routes
console.log("🔗 Setting up routes...");
app.use("/api/auth", authRoutes);
console.log("✅ Auth routes mounted at /api/auth");
app.use("/api/dashboard", dashboardRoutes);
console.log("✅ Dashboard routes mounted at /api/dashboard");
app.use("/api/profile", profileRoutes);
console.log("✅ Profile routes mounted at /api/profile");

// Health check route
console.log("🏥 Setting up health check route...");
app.get("/", (req, res) => {
  console.log("📞 Health check endpoint called");
  res.json({
    message: "🚀 MERN Backend Server is running!",
    status: "active",
    timestamp: new Date().toISOString()
  });
});
console.log("✅ Health check route set up at /");

// Start server
const PORT = process.env.PORT || 5000;
console.log(`🔧 Attempting to start server on port ${PORT}...`);

const server = app.listen(PORT, () => {
  console.log(`🚀 Server successfully running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/`);
  console.log(`🔐 Auth API: http://localhost:${PORT}/api/auth/register`);
});

server.on('error', (error) => {
  console.error('❌ Server failed to start:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use!`);
  }
});
