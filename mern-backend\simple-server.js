const express = require("express");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const cors = require("cors");
require("dotenv").config();

console.log("🔥 STARTING SIMPLE DIRECT SERVER...");

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
.then(() => console.log("✅ MongoDB connected"))
.catch(err => console.error("❌ MongoDB error:", err));

// Simple User Schema
const userSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  dateOfBirth: { type: Date, required: true }
}, { timestamps: true });

const User = mongoose.model("User", userSchema);

// SIMPLE REGISTRATION - JUST POST JSON TO DATABASE
app.post("/register", async (req, res) => {
  console.log("📤 REGISTRATION REQUEST RECEIVED");
  console.log("📝 Data:", req.body);
  
  try {
    const { userId, name, email, password, dateOfBirth } = req.body;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create user
    const newUser = new User({
      userId,
      name,
      email,
      password: hashedPassword,
      dateOfBirth: new Date(dateOfBirth)
    });
    
    // Save to database
    const savedUser = await newUser.save();
    
    console.log("✅ USER SAVED TO DATABASE!");
    console.log("👤 User ID:", savedUser.userId);
    console.log("📧 Email:", savedUser.email);
    
    res.json({
      success: true,
      message: "✅ User registered successfully!",
      user: {
        id: savedUser._id,
        userId: savedUser.userId,
        name: savedUser.name,
        email: savedUser.email,
        dateOfBirth: savedUser.dateOfBirth
      }
    });
    
  } catch (error) {
    console.error("❌ REGISTRATION FAILED:", error.message);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "❌ User already exists (duplicate email or userId)"
      });
    }
    
    res.status(500).json({
      success: false,
      message: "❌ Registration failed: " + error.message
    });
  }
});

// Health check
app.get("/", (req, res) => {
  res.json({ message: "🔥 SIMPLE SERVER RUNNING!" });
});

// Start server
const PORT = 6000;
app.listen(PORT, () => {
  console.log(`🚀 SIMPLE SERVER RUNNING ON PORT ${PORT}`);
  console.log(`📤 Registration endpoint: http://localhost:${PORT}/register`);
  console.log(`🌐 Health check: http://localhost:${PORT}/`);
});
