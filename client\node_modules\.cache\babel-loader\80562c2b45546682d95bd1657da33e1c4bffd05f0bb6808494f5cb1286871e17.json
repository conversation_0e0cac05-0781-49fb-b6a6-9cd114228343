{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MERN1\\\\client\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// API base URL\nconst API_BASE_URL = 'http://localhost:5001/api';\n\n// Configure axios defaults\naxios.defaults.baseURL = API_BASE_URL;\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n\n  // Set axios authorization header\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/profile');\n          setUser(response.data.data.profile);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (emailOrUserId, password) => {\n    try {\n      const response = await axios.post('/auth/login', {\n        emailOrUserId,\n        password\n      });\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      console.log('Sending registration data:', userData);\n      const response = await axios.post('/auth/register', userData);\n      console.log('Registration response:', response.data);\n      const {\n        token: newToken,\n        user: newUser\n      } = response.data;\n      setToken(newToken);\n      setUser(newUser);\n      localStorage.setItem('token', newToken);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data, _error$response4, _error$response4$data, _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n      console.error('Registration error:', error);\n      console.error('Error response:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Registration failed';\n      const errors = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.errors) || [];\n      const details = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.details) || ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.error) || '';\n      return {\n        success: false,\n        message,\n        errors,\n        details\n      };\n    }\n  };\n  const logout = () => {\n    setToken(null);\n    setUser(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const getDashboard = async () => {\n    try {\n      const response = await axios.get('/dashboard');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      const message = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to load dashboard';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const getProfile = async () => {\n    try {\n      const response = await axios.get('/profile');\n      return {\n        success: true,\n        data: response.data.data.profile\n      };\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      const message = ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || 'Failed to load profile';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    getDashboard,\n    getProfile,\n    isAuthenticated: !!token\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"FXi2zprQ4FdDQp5Badf2jWUNTs8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "API_BASE_URL", "defaults", "baseURL", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "headers", "common", "checkAuth", "response", "get", "data", "profile", "error", "console", "logout", "login", "emailOrUserId", "password", "post", "newToken", "userData", "setItem", "success", "message", "_error$response", "_error$response$data", "register", "log", "newUser", "_error$response2", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "errors", "details", "removeItem", "getDashboard", "_error$response7", "_error$response7$data", "getProfile", "_error$response8", "_error$response8$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MERN1/client/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\n// API base URL\nconst API_BASE_URL = 'http://localhost:5001/api';\n\n// Configure axios defaults\naxios.defaults.baseURL = API_BASE_URL;\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n\n  // Set axios authorization header\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/profile');\n          setUser(response.data.data.profile);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (emailOrUserId, password) => {\n    try {\n      const response = await axios.post('/auth/login', {\n        emailOrUserId,\n        password\n      });\n\n      const { token: newToken, user: userData } = response.data;\n      \n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      \n      return { success: true, message: response.data.message };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      return { success: false, message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      console.log('Sending registration data:', userData);\n      const response = await axios.post('/auth/register', userData);\n      console.log('Registration response:', response.data);\n\n      const { token: newToken, user: newUser } = response.data;\n\n      setToken(newToken);\n      setUser(newUser);\n      localStorage.setItem('token', newToken);\n\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      console.error('Registration error:', error);\n      console.error('Error response:', error.response?.data);\n\n      const message = error.response?.data?.message || error.message || 'Registration failed';\n      const errors = error.response?.data?.errors || [];\n      const details = error.response?.data?.details || error.response?.data?.error || '';\n\n      return { success: false, message, errors, details };\n    }\n  };\n\n  const logout = () => {\n    setToken(null);\n    setUser(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const getDashboard = async () => {\n    try {\n      const response = await axios.get('/dashboard');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to load dashboard';\n      return { success: false, message };\n    }\n  };\n\n  const getProfile = async () => {\n    try {\n      const response = await axios.get('/profile');\n      return { success: true, data: response.data.data.profile };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to load profile';\n      return { success: false, message };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    getDashboard,\n    getProfile,\n    isAuthenticated: !!token\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMQ,YAAY,GAAG,2BAA2B;;AAEhD;AACAJ,KAAK,CAACK,QAAQ,CAACC,OAAO,GAAGF,YAAY;AAErC,OAAO,MAAMG,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGX,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACM,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAACqB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACjE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,IAAIiB,KAAK,EAAE;MACThB,KAAK,CAACK,QAAQ,CAACiB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUP,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOhB,KAAK,CAACK,QAAQ,CAACiB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;;EAEX;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMyB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIR,KAAK,EAAE;QACT,IAAI;UACF,MAAMS,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,UAAU,CAAC;UAC5CX,OAAO,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC;QACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CE,MAAM,CAAC,CAAC;QACV;MACF;MACAV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACR,KAAK,CAAC,CAAC;EAEX,MAAMgB,KAAK,GAAG,MAAAA,CAAOC,aAAa,EAAEC,QAAQ,KAAK;IAC/C,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMzB,KAAK,CAACmC,IAAI,CAAC,aAAa,EAAE;QAC/CF,aAAa;QACbC;MACF,CAAC,CAAC;MAEF,MAAM;QAAElB,KAAK,EAAEoB,QAAQ;QAAEtB,IAAI,EAAEuB;MAAS,CAAC,GAAGZ,QAAQ,CAACE,IAAI;MAEzDV,QAAQ,CAACmB,QAAQ,CAAC;MAClBrB,OAAO,CAACsB,QAAQ,CAAC;MACjBnB,YAAY,CAACoB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MAEvC,OAAO;QAAEG,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAEf,QAAQ,CAACE,IAAI,CAACa;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACd,MAAMF,OAAO,GAAG,EAAAC,eAAA,GAAAZ,KAAK,CAACJ,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,cAAc;MAC/D,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAON,QAAQ,IAAK;IACnC,IAAI;MACFP,OAAO,CAACc,GAAG,CAAC,4BAA4B,EAAEP,QAAQ,CAAC;MACnD,MAAMZ,QAAQ,GAAG,MAAMzB,KAAK,CAACmC,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;MAC7DP,OAAO,CAACc,GAAG,CAAC,wBAAwB,EAAEnB,QAAQ,CAACE,IAAI,CAAC;MAEpD,MAAM;QAAEX,KAAK,EAAEoB,QAAQ;QAAEtB,IAAI,EAAE+B;MAAQ,CAAC,GAAGpB,QAAQ,CAACE,IAAI;MAExDV,QAAQ,CAACmB,QAAQ,CAAC;MAClBrB,OAAO,CAAC8B,OAAO,CAAC;MAChB3B,YAAY,CAACoB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MAEvC,OAAO;QAAEG,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAEf,QAAQ,CAACE,IAAI,CAACa;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdxB,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,OAAO,CAACD,KAAK,CAAC,iBAAiB,GAAAiB,gBAAA,GAAEjB,KAAK,CAACJ,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,CAAC;MAEtD,MAAMa,OAAO,GAAG,EAAAO,gBAAA,GAAAlB,KAAK,CAACJ,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAIX,KAAK,CAACW,OAAO,IAAI,qBAAqB;MACvF,MAAMe,MAAM,GAAG,EAAAN,gBAAA,GAAApB,KAAK,CAACJ,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtB,IAAI,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBK,MAAM,KAAI,EAAE;MACjD,MAAMC,OAAO,GAAG,EAAAL,gBAAA,GAAAtB,KAAK,CAACJ,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBI,OAAO,OAAAH,gBAAA,GAAIxB,KAAK,CAACJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBzB,KAAK,KAAI,EAAE;MAElF,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEC,OAAO;QAAEe,MAAM;QAAEC;MAAQ,CAAC;IACrD;EACF,CAAC;EAED,MAAMzB,MAAM,GAAGA,CAAA,KAAM;IACnBd,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;IACbG,YAAY,CAACuC,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOzD,KAAK,CAACK,QAAQ,CAACiB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,YAAY,CAAC;MAC9C,OAAO;QAAEa,OAAO,EAAE,IAAI;QAAEZ,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACd,MAAMpB,OAAO,GAAG,EAAAmB,gBAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBpB,OAAO,KAAI,0BAA0B;MAC3E,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,UAAU,CAAC;MAC5C,OAAO;QAAEa,OAAO,EAAE,IAAI;QAAEZ,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC;MAAQ,CAAC;IAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACd,MAAMvB,OAAO,GAAG,EAAAsB,gBAAA,GAAAjC,KAAK,CAACJ,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBvB,OAAO,KAAI,wBAAwB;MACzE,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMwB,KAAK,GAAG;IACZlD,IAAI;IACJE,KAAK;IACLI,OAAO;IACPY,KAAK;IACLW,QAAQ;IACRZ,MAAM;IACN2B,YAAY;IACZG,UAAU;IACVI,eAAe,EAAE,CAAC,CAACjD;EACrB,CAAC;EAED,oBACEd,OAAA,CAACC,WAAW,CAAC+D,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAApD,QAAA,EAChCA;EAAQ;IAAAuD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzD,GAAA,CAzHWF,YAAY;AAAA4D,EAAA,GAAZ5D,YAAY;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}