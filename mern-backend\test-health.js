const axios = require('axios');

const testHealth = async () => {
  try {
    console.log('Testing health endpoint...');
    
    const response = await axios.get('http://localhost:5000/');
    
    console.log('✅ Health check successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ Health check failed!');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

testHealth();
