const express = require("express");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const cors = require("cors");

console.log("🔥 STARTING ULTRA SIMPLE SERVER...");

const app = express();

// Enable CORS for all origins
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// MongoDB connection
const MONGO_URI = "mongodb+srv://SiddharthYo16:%<EMAIL>/myapp?retryWrites=true&w=majority&appName=sps5";

mongoose.connect(MONGO_URI)
.then(() => {
  console.log("✅ MongoDB connected successfully");
})
.catch(err => {
  console.error("❌ MongoDB connection failed:", err.message);
});

// User Schema
const userSchema = new mongoose.Schema({
  userId: String,
  name: String,
  email: String,
  password: String,
  dateOfBirth: Date
}, { timestamps: true });

const User = mongoose.model("User", userSchema);

// Health check endpoint
app.get("/", (req, res) => {
  console.log("📋 Health check requested");
  res.json({ 
    status: "success",
    message: "🔥 ULTRA SIMPLE SERVER IS RUNNING!",
    timestamp: new Date().toISOString()
  });
});

// Registration endpoint
app.post("/register", async (req, res) => {
  console.log("📤 Registration request received");
  console.log("📝 Request body:", req.body);
  
  try {
    const { userId, name, email, password, dateOfBirth } = req.body;
    
    // Basic validation
    if (!userId || !name || !email || !password || !dateOfBirth) {
      console.log("❌ Missing required fields");
      return res.status(400).json({
        success: false,
        message: "All fields are required"
      });
    }
    
    // Hash password
    console.log("🔐 Hashing password...");
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    console.log("👤 Creating user...");
    const newUser = new User({
      userId: userId,
      name: name,
      email: email,
      password: hashedPassword,
      dateOfBirth: new Date(dateOfBirth)
    });
    
    // Save to database
    console.log("💾 Saving to database...");
    const savedUser = await newUser.save();
    
    console.log("✅ User saved successfully!");
    console.log("🆔 User ID:", savedUser.userId);
    console.log("📧 Email:", savedUser.email);
    
    res.status(201).json({
      success: true,
      message: "User registered successfully!",
      user: {
        id: savedUser._id,
        userId: savedUser.userId,
        name: savedUser.name,
        email: savedUser.email,
        dateOfBirth: savedUser.dateOfBirth
      }
    });
    
  } catch (error) {
    console.error("❌ Registration error:", error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User with this email or userId already exists"
      });
    }
    
    res.status(500).json({
      success: false,
      message: "Registration failed: " + error.message
    });
  }
});

// Start server
const PORT = 7000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 ULTRA SIMPLE SERVER RUNNING ON PORT ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/`);
  console.log(`📤 Registration: http://localhost:${PORT}/register`);
  console.log(`🔗 CORS enabled for all origins`);
});

console.log("🎯 Server setup complete, waiting for connections...");
