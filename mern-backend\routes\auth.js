const express = require("express");
const jwt = require("jsonwebtoken");
const User = require("../models/User");

const router = express.Router();

// Register Route
router.post("/register", async (req, res) => {
  try {
    const { userId, name, email, password, dateOfBirth } = req.body;

    // Validate required fields
    if (!userId || !name || !email || !password || !dateOfBirth) {
      return res.status(400).json({
        success: false,
        message: "❌ All fields are required"
      });
    }

    // Check if user already exists (email or userId)
    const existingUser = await User.findOne({
      $or: [{ email }, { userId }]
    });

    if (existingUser) {
      const field = existingUser.email === email ? "email" : "user ID";
      return res.status(400).json({
        success: false,
        message: `❌ User with this ${field} already exists`
      });
    }

    // Create new user (password will be hashed automatically by the pre-save middleware)
    const newUser = new User({
      userId,
      name,
      email,
      password,
      dateOfBirth: new Date(dateOfBirth)
    });
    await newUser.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: newUser._id, email: newUser.email, userIdField: newUser.userId },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" }
    );

    res.status(201).json({
      success: true,
      message: "✅ User registered successfully",
      token,
      user: {
        id: newUser._id,
        userId: newUser.userId,
        name: newUser.name,
        email: newUser.email,
        dateOfBirth: newUser.dateOfBirth
      }
    });

  } catch (error) {
    console.error("Registration error:", error);
    console.error("Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack
    });

    // Handle validation errors
    if (error.name === "ValidationError") {
      const errors = Object.values(error.errors).map(err => err.message);
      console.error("Validation errors:", errors);
      return res.status(400).json({
        success: false,
        message: "❌ Validation failed",
        errors,
        details: error.message
      });
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `❌ ${field} already exists`,
        details: `A user with this ${field} already exists`
      });
    }

    res.status(500).json({
      success: false,
      message: "❌ Registration failed",
      error: error.message,
      details: error.toString()
    });
  }
});

// Login Route
router.post("/login", async (req, res) => {
  try {
    const { emailOrUserId, password } = req.body;

    // Validate input
    if (!emailOrUserId || !password) {
      return res.status(400).json({
        success: false,
        message: "❌ Email/User ID and password are required"
      });
    }

    // Find user by email or userId
    const user = await User.findOne({
      $or: [{ email: emailOrUserId }, { userId: emailOrUserId }]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "❌ Invalid credentials"
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "❌ Invalid credentials"
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, userIdField: user.userId },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" }
    );

    res.json({
      success: true,
      message: "✅ Login successful",
      token,
      user: {
        id: user._id,
        userId: user.userId,
        name: user.name,
        email: user.email,
        dateOfBirth: user.dateOfBirth
      }
    });

  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "❌ Login failed",
      error: error.message
    });
  }
});

module.exports = router;
