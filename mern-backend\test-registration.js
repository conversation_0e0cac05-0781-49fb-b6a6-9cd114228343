const axios = require('axios');

const testRegistration = async () => {
  try {
    console.log('Testing registration API...');
    
    const testUser = {
      userId: 'testuser123',
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      dateOfBirth: '1990-01-01'
    };
    
    console.log('Sending data:', testUser);
    
    const response = await axios.post('http://localhost:5000/api/auth/register', testUser, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Registration successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ Registration failed!');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

testRegistration();
