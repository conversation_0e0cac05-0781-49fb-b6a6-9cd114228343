const axios = require('axios');

// Test 1: Basic server health check
const testHealth = async () => {
  try {
    console.log('🔍 Testing server health...');
    const response = await axios.get('http://localhost:5001/');
    console.log('✅ Server is responding!');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed!');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    return false;
  }
};

// Test 2: Raw POST test
const testRawPost = async () => {
  try {
    console.log('\n🔍 Testing raw POST to registration endpoint...');

    const rawData = JSON.stringify({
      userId: 'testuser123',
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      dateOfBirth: '1990-01-01'
    });

    console.log('Raw JSON data:', rawData);

    const response = await axios({
      method: 'POST',
      url: 'http://localhost:5001/api/auth/register',
      data: rawData,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ Raw POST successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);

  } catch (error) {
    console.error('❌ Raw POST failed!');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received. Request details:');
      console.error('Request URL:', error.config?.url);
      console.error('Request method:', error.config?.method);
    }
  }
};

// Test 3: Check if routes are loaded
const testRoutes = async () => {
  console.log('\n🔍 Testing different endpoints...');

  const endpoints = [
    'http://localhost:5001/',
    'http://localhost:5001/api',
    'http://localhost:5001/api/auth',
    'http://localhost:5001/api/auth/register'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(endpoint, { timeout: 5000 });
      console.log(`✅ ${endpoint} - Status: ${response.status}`);
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error.response?.status || error.code}`);
    }
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('🚀 Starting comprehensive API tests...\n');

  const serverUp = await testHealth();

  if (serverUp) {
    await testRoutes();
    await testRawPost();
  } else {
    console.log('\n❌ Server is not responding. Check if backend is running on port 5001.');
  }
};

runAllTests();
